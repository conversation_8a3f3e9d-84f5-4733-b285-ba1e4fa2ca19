#!/usr/bin/env python3
"""
Script to inspect database table fields
"""
from app.core.database import get_analyzer

def inspect_table_fields():
    """Inspect the actual fields available in the database tables"""
    analyzer = get_analyzer()
    
    print("🔍 Inspecting database table fields...")
    print("=" * 60)
    
    # Check OF_DA table fields
    print("\n📋 OF_DA Table Fields:")
    print("-" * 30)
    try:
        query_of = "SELECT TOP 1 * FROM gpao.OF_DA WHERE NUMERO_OFDA LIKE 'F%'"
        df_of = analyzer.execute_query(query_of)
        if not df_of.empty:
            print("Available columns:")
            for col in sorted(df_of.columns):
                print(f"  - {col}")
        else:
            print("No data found in OF_DA table")
    except Exception as e:
        print(f"Error querying OF_DA: {e}")
    
    # Check HISTO_OF_DA table fields
    print("\n📋 HISTO_OF_DA Table Fields:")
    print("-" * 30)
    try:
        query_histo = "SELECT TOP 1 * FROM gpao.HISTO_OF_DA WHERE NUMERO_OFDA LIKE 'F%'"
        df_histo = analyzer.execute_query(query_histo)
        if not df_histo.empty:
            print("Available columns:")
            for col in sorted(df_histo.columns):
                print(f"  - {col}")
        else:
            print("No data found in HISTO_OF_DA table")
    except Exception as e:
        print(f"Error querying HISTO_OF_DA: {e}")
    
    # Check for specific date fields
    print("\n📅 Date Field Analysis:")
    print("-" * 30)
    
    # Check OF_DA for date fields
    try:
        query_dates_of = """
        SELECT TOP 5 
            NUMERO_OFDA,
            LANCEMENT_AU_PLUS_TARD
        FROM gpao.OF_DA 
        WHERE NUMERO_OFDA LIKE 'F%' 
        ORDER BY LANCEMENT_AU_PLUS_TARD DESC
        """
        df_dates_of = analyzer.execute_query(query_dates_of)
        print("OF_DA date samples:")
        print(df_dates_of.to_string(index=False))
    except Exception as e:
        print(f"Error getting OF_DA date samples: {e}")
    
    # Check HISTO_OF_DA for date fields
    try:
        query_dates_histo = """
        SELECT TOP 5 
            NUMERO_OFDA,
            LANCEMENT_AU_PLUS_TARD
        FROM gpao.HISTO_OF_DA 
        WHERE NUMERO_OFDA LIKE 'F%' 
        ORDER BY LANCEMENT_AU_PLUS_TARD DESC
        """
        df_dates_histo = analyzer.execute_query(query_dates_histo)
        print("\nHISTO_OF_DA date samples:")
        print(df_dates_histo.to_string(index=False))
    except Exception as e:
        print(f"Error getting HISTO_OF_DA date samples: {e}")

if __name__ == "__main__":
    inspect_table_fields()
