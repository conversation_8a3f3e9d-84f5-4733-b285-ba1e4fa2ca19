#!/usr/bin/env python3
"""
Script to find actual column names in database tables
"""
import pyodbc
from app.core.config import get_settings

def find_table_columns():
    """Find actual column names in OF_DA and HISTO_OF_DA tables"""
    settings = get_settings()
    
    print("🔍 Finding actual column names in database tables...")
    print("=" * 70)
    
    try:
        # Connect to database
        connection_string = (
            f"DRIVER={{SQL Anywhere 17}};"
            f"SERVER={settings.db_server_name};"
            f"HOST={settings.db_host};"
            f"DATABASE={settings.db_database_name};"
            f"UID={settings.db_uid};"
            f"PWD={settings.db_pwd};"
            "CHARSET=UTF-8;"
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Check OF_DA table columns
        print("\n📋 OF_DA Table Columns:")
        print("-" * 40)
        try:
            cursor.execute('SELECT TOP 1 * FROM "gpao"."OF_DA"')
            res = cursor.fetchall()
            columns = [column[0] for column in cursor.description]
            print("Available columns:")
            for i, col in enumerate(sorted(columns), 1):
                print(f"  {i:2d}. {col}")
            print(f"\n📊 Total number of columns = {len(columns)}")
            
            # Look for date-related columns
            date_columns = [col for col in columns if any(word in col.upper() for word in ['DATE', 'LANCE', 'CLOTURE', 'LANCEMENT'])]
            if date_columns:
                print(f"🗓️ Date-related columns found:")
                for col in date_columns:
                    print(f"  - {col}")
            else:
                print("⚠️ No obvious date-related columns found")
                
        except Exception as e:
            print(f"❌ Error querying OF_DA: {e}")
        
        # Check HISTO_OF_DA table columns
        print("\n📋 HISTO_OF_DA Table Columns:")
        print("-" * 40)
        try:
            cursor.execute('SELECT TOP 1 * FROM "gpao"."HISTO_OF_DA"')
            res = cursor.fetchall()
            columns = [column[0] for column in cursor.description]
            print("Available columns:")
            for i, col in enumerate(sorted(columns), 1):
                print(f"  {i:2d}. {col}")
            print(f"\n📊 Total number of columns = {len(columns)}")
            
            # Look for date-related columns
            date_columns = [col for col in columns if any(word in col.upper() for word in ['DATE', 'LANCE', 'CLOTURE', 'LANCEMENT'])]
            if date_columns:
                print(f"🗓️ Date-related columns found:")
                for col in date_columns:
                    print(f"  - {col}")
            else:
                print("⚠️ No obvious date-related columns found")
                
        except Exception as e:
            print(f"❌ Error querying HISTO_OF_DA: {e}")
        
        # Sample some data to see actual values
        print("\n📊 Sample Data Analysis:")
        print("-" * 40)
        
        # Sample OF_DA data
        try:
            cursor.execute('SELECT TOP 3 NUMERO_OFDA, LANCEMENT_AU_PLUS_TARD FROM "gpao"."OF_DA" WHERE NUMERO_OFDA LIKE \'F%\' ORDER BY LANCEMENT_AU_PLUS_TARD DESC')
            rows = cursor.fetchall()
            print("OF_DA sample data:")
            for row in rows:
                print(f"  {row[0]} -> {row[1]}")
        except Exception as e:
            print(f"❌ Error sampling OF_DA: {e}")
        
        # Sample HISTO_OF_DA data
        try:
            cursor.execute('SELECT TOP 3 NUMERO_OFDA, LANCEMENT_AU_PLUS_TARD FROM "gpao"."HISTO_OF_DA" WHERE NUMERO_OFDA LIKE \'F%\' ORDER BY LANCEMENT_AU_PLUS_TARD DESC')
            rows = cursor.fetchall()
            print("HISTO_OF_DA sample data:")
            for row in rows:
                print(f"  {row[0]} -> {row[1]}")
        except Exception as e:
            print(f"❌ Error sampling HISTO_OF_DA: {e}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Database connection error: {e}")

if __name__ == "__main__":
    find_table_columns()
