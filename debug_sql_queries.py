#!/usr/bin/env python3
"""
Debug SQL queries to see what's happening with date filtering
"""
import pyodbc
from app.core.config import get_settings

def debug_sql_queries():
    """Debug the SQL queries directly"""
    print("🔍 Debugging SQL Queries")
    print("=" * 50)
    
    settings = get_settings()
    
    try:
        # Connect to database
        connection_string = (
            f"DRIVER={{SQL Anywhere 17}};"
            f"SERVER={settings.db_server_name};"
            f"HOST={settings.db_host};"
            f"DATABASE={settings.db_database_name};"
            f"UID={settings.db_uid};"
            f"PWD={settings.db_pwd};"
            "CHARSET=UTF-8;"
        )
        
        conn = pyodbc.connect(connection_string)
        cursor = conn.cursor()
        
        # Test 1: Check DATE_CLOTURE filtering in HISTO_OF_DA
        print("\n🧪 Test 1: DATE_CLOTURE filtering in HISTO_OF_DA")
        print("-" * 50)
        
        # Count all records
        cursor.execute('SELECT COUNT(*) FROM "gpao"."HISTO_OF_DA" WHERE NUMERO_OFDA LIKE \'F%\'')
        total_count = cursor.fetchone()[0]
        print(f"📊 Total historical records: {total_count}")
        
        # Count with DATE_CLOTURE filter
        test_queries = [
            ("2024-11-20", "2024-11-25", "November 2024 range"),
            ("2024-01-01", "2024-12-31", "Full 2024"),
            ("2025-01-01", "2025-12-31", "Full 2025")
        ]
        
        for date_debut, date_fin, description in test_queries:
            query = '''
            SELECT COUNT(*) 
            FROM "gpao"."HISTO_OF_DA" 
            WHERE NUMERO_OFDA LIKE 'F%' 
            AND DATE_CLOTURE >= ? 
            AND DATE_CLOTURE <= ?
            '''
            cursor.execute(query, (date_debut, date_fin))
            filtered_count = cursor.fetchone()[0]
            print(f"  📅 {description}: {filtered_count} records")
        
        # Test 2: Check LANCE_LE filtering in OF_DA
        print("\n🧪 Test 2: LANCE_LE filtering in OF_DA")
        print("-" * 50)
        
        # Count all active records
        cursor.execute('SELECT COUNT(*) FROM "gpao"."OF_DA" WHERE NUMERO_OFDA LIKE \'F%\'')
        total_active = cursor.fetchone()[0]
        print(f"📊 Total active records: {total_active}")
        
        for date_debut, date_fin, description in test_queries:
            query = '''
            SELECT COUNT(*) 
            FROM "gpao"."OF_DA" 
            WHERE NUMERO_OFDA LIKE 'F%' 
            AND LANCE_LE >= ? 
            AND LANCE_LE <= ?
            '''
            cursor.execute(query, (date_debut, date_fin))
            filtered_count = cursor.fetchone()[0]
            print(f"  📅 {description}: {filtered_count} records")
        
        # Test 3: Check LANCE_LE filtering in HISTO_OF_DA
        print("\n🧪 Test 3: LANCE_LE filtering in HISTO_OF_DA")
        print("-" * 50)
        
        for date_debut, date_fin, description in test_queries:
            query = '''
            SELECT COUNT(*) 
            FROM "gpao"."HISTO_OF_DA" 
            WHERE NUMERO_OFDA LIKE 'F%' 
            AND LANCE_LE >= ? 
            AND LANCE_LE <= ?
            '''
            cursor.execute(query, (date_debut, date_fin))
            filtered_count = cursor.fetchone()[0]
            print(f"  📅 {description}: {filtered_count} records")
        
        # Test 4: Sample data to see actual date values
        print("\n🧪 Test 4: Sample date values")
        print("-" * 50)
        
        # Sample DATE_CLOTURE values
        cursor.execute('''
        SELECT TOP 5 NUMERO_OFDA, DATE_CLOTURE, LANCE_LE, LANCEMENT_AU_PLUS_TARD 
        FROM "gpao"."HISTO_OF_DA" 
        WHERE NUMERO_OFDA LIKE 'F%' 
        ORDER BY DATE_CLOTURE DESC
        ''')
        
        print("📋 Sample HISTO_OF_DA dates:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: CLOTURE={row[1]}, LANCE_LE={row[2]}, LANCEMENT={row[3]}")
        
        # Sample LANCE_LE values from active
        cursor.execute('''
        SELECT TOP 5 NUMERO_OFDA, LANCE_LE, LANCEMENT_AU_PLUS_TARD 
        FROM "gpao"."OF_DA" 
        WHERE NUMERO_OFDA LIKE 'F%' 
        ORDER BY LANCE_LE DESC
        ''')
        
        print("\n📋 Sample OF_DA dates:")
        for row in cursor.fetchall():
            print(f"  {row[0]}: LANCE_LE={row[1]}, LANCEMENT={row[2]}")
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_sql_queries()
