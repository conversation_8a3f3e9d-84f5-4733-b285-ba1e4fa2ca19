#!/usr/bin/env python3
"""
Debug API calls to see what parameters are being passed
"""
import requests
import json

def debug_api_calls():
    """Debug the API calls to see what's happening"""
    print("🔍 Debugging API Calls")
    print("=" * 50)
    
    base_url = "http://localhost:8008/api/of"
    
    # Test the completed endpoint with date filtering
    print("\n🧪 Testing /completed endpoint")
    print("-" * 40)
    
    test_url = f"{base_url}/completed?dateDebut=2024-11-20&dateFin=2024-11-25"
    print(f"📞 Calling: {test_url}")
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            count = data['data']['count'] if 'data' in data and 'count' in data['data'] else 0
            print(f"📈 Count: {count}")
            
            # Expected: should be around 6 records based on SQL test
            if count == 6:
                print("✅ DATE_CLOTURE filtering working correctly!")
            elif count == 12979:
                print("❌ DATE_CLOTURE filtering NOT working - returning all records")
            else:
                print(f"⚠️ Unexpected count: {count}")
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test the combined endpoint with date filtering
    print("\n🧪 Testing /combined endpoint")
    print("-" * 40)
    
    test_url = f"{base_url}/combined?dateDebut=2025-06-20&dateFin=2025-06-30"
    print(f"📞 Calling: {test_url}")
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            count = data['data']['count'] if 'data' in data and 'count' in data['data'] else 0
            print(f"📈 Count: {count}")
            
            # Expected: should be much less than 13192 based on LANCE_LE filtering
            if count < 1000:
                print("✅ LANCE_LE filtering working correctly!")
            elif count == 13192:
                print("❌ LANCE_LE filtering NOT working - returning all records")
            else:
                print(f"⚠️ Unexpected count: {count}")
                
            # Show sample data to verify filtering
            if count > 0 and 'of_list' in data['data'] and data['data']['of_list']:
                print("📋 Sample records:")
                for i, record in enumerate(data['data']['of_list'][:3]):
                    lance_le = record.get('LANCE_LE', 'N/A')
                    print(f"  {i+1}. {record['NUMERO_OFDA']}: LANCE_LE={lance_le}")
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")
    
    # Test with no date filter to see baseline
    print("\n🧪 Testing /combined endpoint without date filter")
    print("-" * 40)
    
    test_url = f"{base_url}/combined"
    print(f"📞 Calling: {test_url}")
    
    try:
        response = requests.get(test_url, timeout=10)
        print(f"📊 Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            count = data['data']['count'] if 'data' in data and 'count' in data['data'] else 0
            print(f"📈 Count without filter: {count}")
        else:
            print(f"❌ Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    debug_api_calls()
